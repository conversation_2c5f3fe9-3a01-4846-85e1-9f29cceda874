<?php
/**
 * مدير الخدمات المركزية - AYM ERP
 * Central Services Manager
 * 
 * @package    AYM ERP
 * <AUTHOR> Team
 * @copyright  2025 AYM ERP
 * @license    Commercial
 * @version    2.0.0
 */

class ModelCoreCentralServiceManager extends Model {
    
    /**
     * تهيئة الخدمات المركزية
     */
    public function __construct($registry) {
        parent::__construct($registry);
        $this->initializeCentralServices();
    }
    
    /**
     * تهيئة جميع الخدمات المركزية
     */
    private function initializeCentralServices() {
        // تحميل الخدمات الأساسية
        $this->loadCoreServices();
        
        // تحميل خدمات التواصل
        $this->loadCommunicationServices();
        
        // تحميل خدمات سير العمل
        $this->loadWorkflowServices();
        
        // تحميل خدمات التدقيق
        $this->loadAuditServices();
    }
    
    /**
     * تحميل الخدمات الأساسية
     */
    private function loadCoreServices() {
        // خدمة الإشعارات الموحدة
        $this->load->model('communication/unified_notification');

        // خدمة المستندات الموحدة
        $this->load->model('unified_document');

        // ملاحظة: user_activity يتم تحميله من مجلد logging وفق الدستور الشامل
    }
    
    /**
     * تحميل خدمات التواصل
     */
    private function loadCommunicationServices() {
        // خدمة الرسائل
        $this->load->model('communication/messages');
        
        // خدمة الإعلانات
        $this->load->model('communication/announcements');
        
        // خدمة الدردشة
        $this->load->model('communication/chat');
        
        // خدمة الفرق
        $this->load->model('communication/teams');
    }
    
    /**
     * تحميل خدمات سير العمل
     */
    private function loadWorkflowServices() {
        // خدمة سير العمل المرئي
        $this->load->model('workflow/visual_workflow_engine');
        
        // خدمة الموافقات
        $this->load->model('workflow/approval');
        
        // خدمة المهام
        $this->load->model('workflow/task');
    }
    
    /**
     * تحميل خدمات التدقيق
     */
    private function loadAuditServices() {
        // خدمة مسار التدقيق
        $this->load->model('tool/audit');
        
        // خدمة سجلات النظام
        $this->load->model('logging/system_logs');
        
        // خدمة نشاط المستخدمين
        $this->load->model('logging/user_activity');
    }
    
    /**
     * الحصول على خدمة محددة
     */
    public function getService($serviceName) {
        $serviceMap = array(
            'notifications' => 'model_communication_unified_notification',
            'documents' => 'model_unified_document',
            'messages' => 'model_communication_messages',
            'announcements' => 'model_communication_announcements',
            'chat' => 'model_communication_chat',
            'teams' => 'model_communication_teams',
            'workflow' => 'model_workflow_visual_workflow_engine',
            'approval' => 'model_workflow_approval',
            'tasks' => 'model_workflow_task',
            'audit' => 'model_tool_audit',
            'system_logs' => 'model_logging_system_logs',
            'user_activity' => 'model_logging_user_activity'
        );

        if (isset($serviceMap[$serviceName])) {
            $modelName = $serviceMap[$serviceName];

            // تحميل الخدمة المطلوبة حسب الحاجة

            if (isset($this->$modelName)) {
                return $this->$modelName;
            }
        }

        throw new Exception("Service '{$serviceName}' not found or not loaded");
    }
    
    /**
     * تسجيل نشاط في النظام
     */
    public function logActivity($action_type, $module, $description, $additional_data = array()) {
        try {
            // إضافة معلومات الوحدة للبيانات الإضافية
            $additional_data['module'] = $module;

            return $this->getService('user_activity')->logUserActivity($action_type, $description, $additional_data);

        } catch (Exception $e) {
            // في حالة فشل تسجيل النشاط، لا نريد إيقاف العملية الأساسية
            error_log("Failed to log activity: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إرسال إشعار موحد
     */
    public function sendNotification($type, $title, $message, $recipients = array(), $additional_data = array()) {
        try {
            $notification_data = array(
                'type' => $type,
                'title' => $title,
                'message' => $message,
                'sender_id' => $this->user->getId(),
                'recipients' => $recipients,
                'additional_data' => $additional_data
            );
            
            return $this->getService('notifications')->createNotification($notification_data);
            
        } catch (Exception $e) {
            error_log("Failed to send notification: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إنشاء مستند موحد
     */
    public function createDocument($title, $content, $type, $metadata = array()) {
        try {
            $document_data = array(
                'title' => $title,
                'content' => $content,
                'type' => $type,
                'created_by' => $this->user->getId(),
                'metadata' => $metadata
            );
            
            return $this->getService('documents')->createDocument($document_data);
            
        } catch (Exception $e) {
            error_log("Failed to create document: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * بدء سير عمل
     */
    public function startWorkflow($workflow_id, $data = array()) {
        try {
            return $this->getService('workflow')->startWorkflow($workflow_id, $data);

        } catch (Exception $e) {
            error_log("Failed to start workflow: " . $e->getMessage());
            return false;
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال تسجيل الأنشطة المتخصصة (من central_logging.php)
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * تسجيل عملية إنشاء
     */
    public function logCreate($module, $reference_type, $reference_id, $description = '', $data = []) {
        $additional_data = array_merge($data, [
            'reference_type' => $reference_type,
            'reference_id' => $reference_id
        ]);

        if (empty($description)) {
            $description = "تم إنشاء {$reference_type} جديد";
        }

        return $this->logActivity('create', $module, $description, $additional_data);
    }

    /**
     * تسجيل عملية تحديث
     */
    public function logUpdate($module, $reference_type, $reference_id, $before_data = [], $after_data = [], $description = '') {
        $additional_data = [
            'reference_type' => $reference_type,
            'reference_id' => $reference_id,
            'before_data' => $before_data,
            'after_data' => $after_data
        ];

        if (empty($description)) {
            $description = "تم تحديث {$reference_type}";
        }

        return $this->logActivity('update', $module, $description, $additional_data);
    }

    /**
     * تسجيل عملية حذف
     */
    public function logDelete($module, $reference_type, $reference_id, $data = [], $description = '') {
        $additional_data = [
            'reference_type' => $reference_type,
            'reference_id' => $reference_id,
            'before_data' => $data
        ];

        if (empty($description)) {
            $description = "تم حذف {$reference_type}";
        }

        return $this->logActivity('delete', $module, $description, $additional_data);
    }

    /**
     * تسجيل عملية عرض/مشاهدة
     */
    public function logView($module, $reference_type, $reference_id, $description = '') {
        $additional_data = [
            'reference_type' => $reference_type,
            'reference_id' => $reference_id
        ];

        if (empty($description)) {
            $description = "تم عرض {$reference_type}";
        }

        return $this->logActivity('view', $module, $description, $additional_data);
    }

    /**
     * تسجيل دخول المستخدم
     */
    public function logLogin($user_id, $success = true, $description = '') {
        $action_type = $success ? 'login_success' : 'login_failed';

        if (empty($description)) {
            $description = $success ? 'تم تسجيل الدخول بنجاح' : 'فشل تسجيل الدخول';
        }

        $additional_data = [
            'reference_type' => 'user',
            'reference_id' => $user_id,
            'success' => $success
        ];

        return $this->logActivity($action_type, 'authentication', $description, $additional_data);
    }

    /**
     * تسجيل خروج المستخدم
     */
    public function logLogout($user_id, $description = '') {
        if (empty($description)) {
            $description = 'تم تسجيل الخروج';
        }

        $additional_data = [
            'reference_type' => 'user',
            'reference_id' => $user_id
        ];

        return $this->logActivity('logout', 'authentication', $description, $additional_data);
    }

    /**
     * تسجيل خطأ
     */
    public function logError($module, $error_message, $data = []) {
        return $this->logActivity('error', $module, $error_message, $data);
    }

    /**
     * تسجيل تحذير
     */
    public function logWarning($module, $warning_message, $data = []) {
        return $this->logActivity('warning', $module, $warning_message, $data);
    }

    /**
     * تسجيل معلومات
     */
    public function logInfo($module, $info_message, $data = []) {
        return $this->logActivity('info', $module, $info_message, $data);
    }

    /**
     * تسجيل عملية موافقة
     */
    public function logApproval($module, $reference_type, $reference_id, $approved = true, $description = '') {
        $action_type = $approved ? 'approve' : 'reject';

        if (empty($description)) {
            $description = $approved ? 'تمت الموافقة' : 'تم الرفض';
        }

        $additional_data = [
            'reference_type' => $reference_type,
            'reference_id' => $reference_id,
            'approved' => $approved
        ];

        return $this->logActivity($action_type, $module, $description, $additional_data);
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال المستندات المتقدمة (من unified_document.php و cart/aym.php)
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * رفع وتخزين مستند جديد
     */
    public function uploadDocument($file, $data, $reference_module = null, $reference_id = null) {
        try {
            // التحقق من الملف
            if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
                return false;
            }

            // إنشاء مسار حفظ الملفات
            $upload_dir = DIR_UPLOAD . 'documents/' . date('Y/m/d/');
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            // إنشاء اسم فريد للملف
            $filename = date('YmdHis') . '_' . uniqid() . '_' . $this->cleanFilename($file['name']);
            $file_path = $upload_dir . $filename;

            // نقل الملف إلى المسار
            if (!move_uploaded_file($file['tmp_name'], $file_path)) {
                return false;
            }

            // حفظ بيانات المستند
            $document_data = [
                'title' => $data['title'],
                'description' => $data['description'] ?? '',
                'document_type' => $data['document_type'] ?? 'other',
                'file_path' => str_replace(DIR_UPLOAD, '', $file_path),
                'file_name' => $file['name'],
                'file_size' => $file['size'],
                'file_type' => $file['type'],
                'created_by' => $this->user->getId(),
                'reference_module' => $reference_module,
                'reference_id' => $reference_id,
                'tags' => $data['tags'] ?? null,
                'status' => $data['status'] ?? 'active'
            ];

            $document_id = $this->getService('documents')->createDocument($document_data);

            // تسجيل النشاط
            $this->logCreate('documents', 'document', $document_id, "تم رفع مستند جديد: {$data['title']}");

            return $document_id;

        } catch (Exception $e) {
            error_log("Failed to upload document: " . $e->getMessage());
            return false;
        }
    }

    /**
     * البحث في المستندات
     */
    public function searchDocuments($query, $filters = [], $limit = 20) {
        try {
            return $this->getService('documents')->searchDocuments($query, $filters, $limit);
        } catch (Exception $e) {
            error_log("Failed to search documents: " . $e->getMessage());
            return [];
        }
    }

    /**
     * مشاركة مستند
     */
    public function shareDocument($document_id, $user_ids, $permission_level = 'read') {
        try {
            $result = $this->getService('documents')->shareDocument($document_id, $user_ids, $permission_level);

            // تسجيل النشاط
            $this->logActivity('document_shared', 'documents', "تم مشاركة المستند #{$document_id}", [
                'document_id' => $document_id,
                'shared_with' => $user_ids,
                'permission_level' => $permission_level
            ]);

            return $result;
        } catch (Exception $e) {
            error_log("Failed to share document: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تنظيف اسم الملف
     */
    private function cleanFilename($filename) {
        // إزالة الأحرف الخطيرة
        $filename = preg_replace('/[^a-zA-Z0-9\._\-\u0600-\u06FF]/', '_', $filename);
        // تحديد الطول الأقصى
        if (strlen($filename) > 100) {
            $extension = pathinfo($filename, PATHINFO_EXTENSION);
            $name = pathinfo($filename, PATHINFO_FILENAME);
            $filename = substr($name, 0, 90) . '.' . $extension;
        }
        return $filename;
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال الإشعارات المتقدمة (من common/notification.php و tool/notification.php)
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * الحصول على إشعارات المستخدم
     */
    public function getUserNotifications($user_id, $start = 0, $limit = 10) {
        try {
            return $this->getService('notifications')->getUserNotifications($user_id, $start, $limit);
        } catch (Exception $e) {
            error_log("Failed to get user notifications: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على عدد الإشعارات غير المقروءة
     */
    public function getUnreadNotificationsCount($user_id) {
        try {
            return $this->getService('notifications')->getUnreadCount($user_id);
        } catch (Exception $e) {
            error_log("Failed to get unread notifications count: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * تحديد إشعار كمقروء
     */
    public function markNotificationAsRead($notification_id, $user_id) {
        try {
            $result = $this->getService('notifications')->markAsRead($notification_id, $user_id);

            // تسجيل النشاط
            $this->logActivity('notification_read', 'notifications', "تم قراءة الإشعار #{$notification_id}", [
                'notification_id' => $notification_id,
                'user_id' => $user_id
            ]);

            return $result;
        } catch (Exception $e) {
            error_log("Failed to mark notification as read: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديد جميع الإشعارات كمقروءة
     */
    public function markAllNotificationsAsRead($user_id) {
        try {
            $result = $this->getService('notifications')->markAllAsRead($user_id);

            // تسجيل النشاط
            $this->logActivity('notifications_read_all', 'notifications', "تم تحديد جميع الإشعارات كمقروءة", [
                'user_id' => $user_id
            ]);

            return $result;
        } catch (Exception $e) {
            error_log("Failed to mark all notifications as read: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال إشعار جماعي
     */
    public function sendBulkNotification($user_ids, $title, $message, $type = 'info', $additional_data = []) {
        try {
            $results = [];
            foreach ($user_ids as $user_id) {
                $results[$user_id] = $this->sendNotification($type, $title, $message, [$user_id], $additional_data);
            }

            // تسجيل النشاط
            $this->logActivity('bulk_notification_sent', 'notifications', "تم إرسال إشعار جماعي: {$title}", [
                'recipients_count' => count($user_ids),
                'type' => $type
            ]);

            return $results;
        } catch (Exception $e) {
            error_log("Failed to send bulk notification: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء إشعار نظام
     */
    public function createSystemNotification($title, $message, $category = 'system', $priority = 'medium') {
        try {
            $notification_data = [
                'type' => 'system',
                'title' => $title,
                'message' => $message,
                'category' => $category,
                'priority' => $priority,
                'sender_id' => 0, // نظام
                'recipients' => [], // للجميع
                'is_system' => true
            ];

            $result = $this->getService('notifications')->createNotification($notification_data);

            // تسجيل النشاط
            $this->logActivity('system_notification_created', 'notifications', "تم إنشاء إشعار نظام: {$title}");

            return $result;
        } catch (Exception $e) {
            error_log("Failed to create system notification: " . $e->getMessage());
            return false;
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال الاستعلام والإحصائيات (من user_activity.php)
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * الحصول على الأنشطة مع فلترة
     */
    public function getActivities($filters = [], $start = 0, $limit = 20) {
        try {
            return $this->getService('user_activity')->getActivities($filters, $start, $limit);
        } catch (Exception $e) {
            error_log("Failed to get activities: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على أنشطة مستخدم محدد
     */
    public function getUserActivities($user_id, $start = 0, $limit = 20) {
        try {
            return $this->getService('user_activity')->getUserActivities($user_id, $start, $limit);
        } catch (Exception $e) {
            error_log("Failed to get user activities: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على أنشطة وحدة محددة
     */
    public function getModuleActivities($module, $start = 0, $limit = 20) {
        try {
            return $this->getService('user_activity')->getModuleActivities($module, $start, $limit);
        } catch (Exception $e) {
            error_log("Failed to get module activities: " . $e->getMessage());
            return [];
        }
    }

    /**
     * البحث في الأنشطة
     */
    public function searchActivities($search_term, $start = 0, $limit = 20) {
        try {
            return $this->getService('user_activity')->searchActivities($search_term, $start, $limit);
        } catch (Exception $e) {
            error_log("Failed to search activities: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على إحصائيات الأنشطة
     */
    public function getActivityStats($user_id = null, $module = null) {
        try {
            return $this->getService('user_activity')->getActivityStats($user_id, $module);
        } catch (Exception $e) {
            error_log("Failed to get activity stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * تصدير الأنشطة
     */
    public function exportActivities($filters = [], $format = 'csv') {
        try {
            return $this->getService('user_activity')->exportActivities($filters, $format);
        } catch (Exception $e) {
            error_log("Failed to export activities: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تنظيف الأنشطة القديمة
     */
    public function cleanOldActivities($days = 365) {
        try {
            $result = $this->getService('user_activity')->cleanOldActivities($days);

            // تسجيل النشاط
            $this->logActivity('activities_cleaned', 'system', "تم تنظيف الأنشطة الأقدم من {$days} يوم", [
                'days' => $days,
                'cleaned_count' => $result
            ]);

            return $result;
        } catch (Exception $e) {
            error_log("Failed to clean old activities: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على إحصائيات المستندات
     */
    public function getDocumentStats($user_id = null) {
        try {
            return $this->getService('documents')->getDocumentStats($user_id);
        } catch (Exception $e) {
            error_log("Failed to get document stats: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على استخدام التخزين
     */
    public function getStorageUsage($user_id = null) {
        try {
            return $this->getService('documents')->getStorageUsage($user_id);
        } catch (Exception $e) {
            error_log("Failed to get storage usage: " . $e->getMessage());
            return 0;
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال التواصل الداخلي المتقدمة (من communication/)
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * إرسال رسالة داخلية
     */
    public function sendInternalMessage($recipients, $subject, $message, $priority = 'normal', $attachments = []) {
        try {
            $message_data = [
                'sender_id' => $this->user->getId(),
                'recipients' => $recipients,
                'subject' => $subject,
                'message' => $message,
                'priority' => $priority,
                'attachments' => $attachments,
                'message_type' => 'internal',
                'status' => 'sent'
            ];

            $message_id = $this->getService('communication')->sendMessage($message_data);

            // تسجيل النشاط
            $this->logCreate('communication', 'message', $message_id, "تم إرسال رسالة داخلية: {$subject}", [
                'recipients_count' => count($recipients),
                'priority' => $priority
            ]);

            // إرسال إشعار للمستقبلين
            $this->sendNotification('new_message', 'رسالة جديدة', "لديك رسالة جديدة من " . $this->user->getFirstName(), $recipients, [
                'message_id' => $message_id,
                'sender_name' => $this->user->getFirstName() . ' ' . $this->user->getLastName()
            ]);

            return $message_id;

        } catch (Exception $e) {
            error_log("Failed to send internal message: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إنشاء إعلان عام
     */
    public function createAnnouncement($title, $content, $target_groups = [], $priority = 'normal', $expires_at = null) {
        try {
            $announcement_data = [
                'title' => $title,
                'content' => $content,
                'created_by' => $this->user->getId(),
                'target_groups' => $target_groups,
                'priority' => $priority,
                'expires_at' => $expires_at,
                'status' => 'active'
            ];

            $announcement_id = $this->getService('communication')->createAnnouncement($announcement_data);

            // تسجيل النشاط
            $this->logCreate('communication', 'announcement', $announcement_id, "تم إنشاء إعلان جديد: {$title}");

            // إرسال إشعار للمجموعات المستهدفة
            if (!empty($target_groups)) {
                $this->sendNotification('new_announcement', 'إعلان جديد', $title, [], [
                    'announcement_id' => $announcement_id,
                    'target_groups' => $target_groups
                ]);
            }

            return $announcement_id;

        } catch (Exception $e) {
            error_log("Failed to create announcement: " . $e->getMessage());
            return false;
        }
    }

    /**
     * بدء محادثة جماعية
     */
    public function startGroupChat($participants, $chat_name, $description = '') {
        try {
            $chat_data = [
                'chat_name' => $chat_name,
                'description' => $description,
                'created_by' => $this->user->getId(),
                'participants' => $participants,
                'chat_type' => 'group',
                'status' => 'active'
            ];

            $chat_id = $this->getService('communication')->createChat($chat_data);

            // تسجيل النشاط
            $this->logCreate('communication', 'chat', $chat_id, "تم إنشاء محادثة جماعية: {$chat_name}", [
                'participants_count' => count($participants)
            ]);

            // إشعار المشاركين
            $this->sendNotification('chat_invitation', 'دعوة للمحادثة', "تمت دعوتك للانضمام إلى محادثة: {$chat_name}", $participants, [
                'chat_id' => $chat_id,
                'chat_name' => $chat_name
            ]);

            return $chat_id;

        } catch (Exception $e) {
            error_log("Failed to start group chat: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إرسال رسالة في المحادثة
     */
    public function sendChatMessage($chat_id, $message, $message_type = 'text', $attachments = []) {
        try {
            $message_data = [
                'chat_id' => $chat_id,
                'sender_id' => $this->user->getId(),
                'message' => $message,
                'message_type' => $message_type,
                'attachments' => $attachments
            ];

            $message_id = $this->getService('communication')->sendChatMessage($message_data);

            // تسجيل النشاط
            $this->logActivity('chat_message_sent', 'communication', "تم إرسال رسالة في المحادثة #{$chat_id}", [
                'chat_id' => $chat_id,
                'message_type' => $message_type
            ]);

            return $message_id;

        } catch (Exception $e) {
            error_log("Failed to send chat message: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على رسائل المستخدم
     */
    public function getUserMessages($user_id, $folder = 'inbox', $start = 0, $limit = 20) {
        try {
            return $this->getService('communication')->getUserMessages($user_id, $folder, $start, $limit);
        } catch (Exception $e) {
            error_log("Failed to get user messages: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على الإعلانات النشطة
     */
    public function getActiveAnnouncements($user_groups = [], $limit = 10) {
        try {
            return $this->getService('communication')->getActiveAnnouncements($user_groups, $limit);
        } catch (Exception $e) {
            error_log("Failed to get active announcements: " . $e->getMessage());
            return [];
        }
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // دوال محرر سير العمل المرئي (من workflow/)
    // ═══════════════════════════════════════════════════════════════════════════════

    /**
     * إنشاء سير عمل جديد
     */
    public function createWorkflow($name, $description, $workflow_data, $category = 'general') {
        try {
            $workflow_definition = [
                'name' => $name,
                'description' => $description,
                'workflow_data' => json_encode($workflow_data),
                'category' => $category,
                'created_by' => $this->user->getId(),
                'status' => 'draft',
                'version' => '1.0'
            ];

            $workflow_id = $this->getService('workflow')->createWorkflow($workflow_definition);

            // تسجيل النشاط
            $this->logCreate('workflow', 'workflow', $workflow_id, "تم إنشاء سير عمل جديد: {$name}");

            return $workflow_id;

        } catch (Exception $e) {
            error_log("Failed to create workflow: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تحديث سير العمل
     */
    public function updateWorkflow($workflow_id, $workflow_data, $version_notes = '') {
        try {
            // الحصول على البيانات القديمة
            $old_workflow = $this->getService('workflow')->getWorkflow($workflow_id);

            $update_data = [
                'workflow_data' => json_encode($workflow_data),
                'version_notes' => $version_notes,
                'updated_by' => $this->user->getId(),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            $result = $this->getService('workflow')->updateWorkflow($workflow_id, $update_data);

            // تسجيل النشاط
            $this->logUpdate('workflow', 'workflow', $workflow_id,
                ['version' => $old_workflow['version']],
                ['version' => $old_workflow['version'] + 0.1],
                "تم تحديث سير العمل: {$old_workflow['name']}"
            );

            return $result;

        } catch (Exception $e) {
            error_log("Failed to update workflow: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تفعيل سير العمل
     */
    public function activateWorkflow($workflow_id) {
        try {
            $result = $this->getService('workflow')->activateWorkflow($workflow_id);

            // تسجيل النشاط
            $this->logActivity('workflow_activated', 'workflow', "تم تفعيل سير العمل #{$workflow_id}", [
                'workflow_id' => $workflow_id
            ]);

            return $result;

        } catch (Exception $e) {
            error_log("Failed to activate workflow: " . $e->getMessage());
            return false;
        }
    }

    /**
     * بدء تنفيذ سير العمل
     */
    public function executeWorkflow($workflow_id, $input_data = [], $context = []) {
        try {
            $execution_data = [
                'workflow_id' => $workflow_id,
                'input_data' => $input_data,
                'context' => $context,
                'started_by' => $this->user->getId(),
                'status' => 'running'
            ];

            $execution_id = $this->getService('workflow')->executeWorkflow($execution_data);

            // تسجيل النشاط
            $this->logActivity('workflow_executed', 'workflow', "تم بدء تنفيذ سير العمل #{$workflow_id}", [
                'workflow_id' => $workflow_id,
                'execution_id' => $execution_id
            ]);

            return $execution_id;

        } catch (Exception $e) {
            error_log("Failed to execute workflow: " . $e->getMessage());
            return false;
        }
    }

    /**
     * إيقاف تنفيذ سير العمل
     */
    public function stopWorkflowExecution($execution_id, $reason = '') {
        try {
            $result = $this->getService('workflow')->stopExecution($execution_id, $reason);

            // تسجيل النشاط
            $this->logActivity('workflow_stopped', 'workflow', "تم إيقاف تنفيذ سير العمل", [
                'execution_id' => $execution_id,
                'reason' => $reason
            ]);

            return $result;

        } catch (Exception $e) {
            error_log("Failed to stop workflow execution: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على حالة تنفيذ سير العمل
     */
    public function getWorkflowExecutionStatus($execution_id) {
        try {
            return $this->getService('workflow')->getExecutionStatus($execution_id);
        } catch (Exception $e) {
            error_log("Failed to get workflow execution status: " . $e->getMessage());
            return null;
        }
    }

    /**
     * الحصول على قائمة سير العمل
     */
    public function getWorkflows($filters = [], $start = 0, $limit = 20) {
        try {
            return $this->getService('workflow')->getWorkflows($filters, $start, $limit);
        } catch (Exception $e) {
            error_log("Failed to get workflows: " . $e->getMessage());
            return [];
        }
    }

    /**
     * الحصول على قوالب سير العمل
     */
    public function getWorkflowTemplates($category = null) {
        try {
            return $this->getService('workflow')->getTemplates($category);
        } catch (Exception $e) {
            error_log("Failed to get workflow templates: " . $e->getMessage());
            return [];
        }
    }

    /**
     * إنشاء سير عمل من قالب
     */
    public function createWorkflowFromTemplate($template_id, $name, $customizations = []) {
        try {
            $template = $this->getService('workflow')->getTemplate($template_id);

            // تطبيق التخصيصات على القالب
            $workflow_data = array_merge($template['workflow_data'], $customizations);

            $workflow_id = $this->createWorkflow($name, $template['description'], $workflow_data, $template['category']);

            // تسجيل النشاط
            $this->logActivity('workflow_created_from_template', 'workflow', "تم إنشاء سير عمل من قالب: {$name}", [
                'template_id' => $template_id,
                'workflow_id' => $workflow_id
            ]);

            return $workflow_id;

        } catch (Exception $e) {
            error_log("Failed to create workflow from template: " . $e->getMessage());
            return false;
        }
    }

    /**
     * تصدير سير العمل
     */
    public function exportWorkflow($workflow_id, $format = 'json') {
        try {
            $result = $this->getService('workflow')->exportWorkflow($workflow_id, $format);

            // تسجيل النشاط
            $this->logActivity('workflow_exported', 'workflow', "تم تصدير سير العمل #{$workflow_id}", [
                'workflow_id' => $workflow_id,
                'format' => $format
            ]);

            return $result;

        } catch (Exception $e) {
            error_log("Failed to export workflow: " . $e->getMessage());
            return false;
        }
    }

    /**
     * استيراد سير العمل
     */
    public function importWorkflow($workflow_data, $name = null) {
        try {
            $import_data = [
                'workflow_data' => $workflow_data,
                'name' => $name ?: 'سير عمل مستورد',
                'imported_by' => $this->user->getId(),
                'status' => 'draft'
            ];

            $workflow_id = $this->getService('workflow')->importWorkflow($import_data);

            // تسجيل النشاط
            $this->logActivity('workflow_imported', 'workflow', "تم استيراد سير عمل جديد", [
                'workflow_id' => $workflow_id
            ]);

            return $workflow_id;

        } catch (Exception $e) {
            error_log("Failed to import workflow: " . $e->getMessage());
            return false;
        }
    }

    /**
     * الحصول على حالة الخدمات المركزية
     */
    public function getServicesStatus() {
        $status = array();
        
        $services = array(
            'notifications', 'documents', 'messages',
            'announcements', 'chat', 'teams', 'workflow', 'approval',
            'tasks', 'audit', 'system_logs', 'user_activity'
        );
        
        foreach ($services as $service) {
            try {
                $this->getService($service);
                $status[$service] = 'active';
            } catch (Exception $exception) {
                $status[$service] = 'inactive - ' . $exception->getMessage();
            }
        }
        
        return $status;
    }
    
    /**
     * تنظيف الخدمات (إزالة البيانات القديمة)
     */
    public function cleanupServices($days = 30) {
        try {
            // تنظيف سجلات الأنشطة القديمة
            $this->getService('user_activity')->cleanupOldLogs($days);
            
            // تنظيف الإشعارات المقروءة القديمة
            $this->getService('notifications')->cleanupReadNotifications($days);
            
            // تنظيف سجلات النظام القديمة
            $this->getService('system_logs')->cleanupOldLogs($days);
            
            return true;
            
        } catch (Exception $e) {
            error_log("Failed to cleanup services: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * إحصائيات الخدمات المركزية
     */
    public function getServicesStatistics() {
        $stats = array();
        
        try {
            // إحصائيات الإشعارات
            $stats['notifications'] = $this->getService('notifications')->getStatistics();
            
            // إحصائيات الأنشطة
            $stats['activities'] = $this->getService('user_activity')->getStatistics();
            
            // إحصائيات المستندات
            $stats['documents'] = $this->getService('documents')->getStatistics();
            
            // إحصائيات سير العمل
            $stats['workflows'] = $this->getService('workflow')->getStatistics();
            
        } catch (Exception $e) {
            error_log("Failed to get services statistics: " . $e->getMessage());
        }
        
        return $stats;
    }

    // ═══════════════════════════════════════════════════════════════════════════════
    // ملاحظات التطوير المحدثة
    // ═══════════════════════════════════════════════════════════════════════════════
    //
    // تم نقل الوظائف التالية من الملفات المراد حذفها:
    //
    // 1. من system/library/central_logging.php:
    //    - logCreate(), logUpdate(), logDelete(), logView()
    //    - logLogin(), logLogout(), logError(), logWarning(), logInfo()
    //    - logApproval()
    //
    // 2. من dashboard/model/logging/user_activity.php:
    //    - getActivities(), getUserActivities(), getModuleActivities()
    //    - searchActivities(), getActivityStats(), exportActivities()
    //    - cleanOldActivities()
    //
    // 3. من dashboard/model/common/notification.php:
    //    - getUserNotifications(), getUnreadNotificationsCount()
    //    - markNotificationAsRead(), markAllNotificationsAsRead()
    //    - sendBulkNotification(), createSystemNotification()
    //
    // 4. من dashboard/model/unified_document.php:
    //    - uploadDocument(), searchDocuments(), shareDocument()
    //    - getDocumentStats(), getStorageUsage()
    //
    // 5. من system/library/cart/aym.php:
    //    - cleanFilename() (دالة تنظيف أسماء الملفات)
    //
    // 6. من dashboard/model/communication/ (التواصل الداخلي):
    //    - sendInternalMessage(), createAnnouncement(), startGroupChat()
    //    - sendChatMessage(), getUserMessages(), getActiveAnnouncements()
    //
    // 7. من dashboard/model/workflow/ (محرر سير العمل المرئي):
    //    - createWorkflow(), updateWorkflow(), activateWorkflow()
    //    - executeWorkflow(), stopWorkflowExecution(), getWorkflowExecutionStatus()
    //    - getWorkflows(), getWorkflowTemplates(), createWorkflowFromTemplate()
    //    - exportWorkflow(), importWorkflow()
    //
    // جميع هذه الوظائف متاحة الآن من خلال central_service_manager
    // ويمكن استدعاؤها من أي كونترولر باستخدام:
    // $this->load->model('core/central_service_manager');
    // $this->model_core_central_service_manager->methodName();
    //
    // أمثلة الاستخدام:
    // - إرسال رسالة: $this->model_core_central_service_manager->sendInternalMessage($recipients, $subject, $message);
    // - إنشاء سير عمل: $this->model_core_central_service_manager->createWorkflow($name, $description, $workflow_data);
    // - تسجيل نشاط: $this->model_core_central_service_manager->logCreate('sales', 'order', $order_id);
    // - إرسال إشعار: $this->model_core_central_service_manager->sendNotification('info', $title, $message, $recipients);
    //
    // تاريخ النقل: 2025-01-16
    // آخر تحديث: 2025-01-16 (إضافة التواصل الداخلي ومحرر سير العمل)
    // ═══════════════════════════════════════════════════════════════════════════════
}
?>
